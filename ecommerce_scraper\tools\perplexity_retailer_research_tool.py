"""Perplexity-powered Retailer Research Tool for product-specific searches."""

import json
import logging
from typing import Dict, Any, List, Optional
from crewai.tools import BaseTool
from pydantic import BaseModel, Field
import requests
import os

logger = logging.getLogger(__name__)


class RetailerResearchInput(BaseModel):
    """Input schema for retailer research."""
    product_query: str = Field(..., description="Specific product to search for (e.g., 'iPhone 15 Pro', 'Samsung Galaxy S24')")
    max_retailers: int = Field(5, description="Maximum number of retailers to find")
    include_comparison_sites: bool = Field(False, description="Whether to include price comparison sites")


class PerplexityRetailerResearchTool(BaseTool):
    """Perplexity-powered tool for finding UK retailers that sell specific products."""

    name: str = "perplexity_retailer_research_tool"
    description: str = """
    AI-powered retailer discovery tool using Perplexity AI for product-specific searches.
    
    Features:
    - Find legitimate UK retailers that sell specific products
    - Exclude price comparison sites (unless specifically requested)
    - Focus on major UK retailers like ASDA, Tesco, Amazon UK, eBay UK, etc.
    - Provide direct product URLs where possible
    - Research product availability and pricing information
    
    Use this tool to discover which UK retailers sell a specific product and get
    direct links to product pages for targeted scraping.
    """
    args_schema: type[BaseModel] = RetailerResearchInput

    def __init__(self):
        """Initialize the Perplexity retailer research tool."""
        super().__init__()
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

        # Get API key from environment or config
        self._api_key = os.getenv("PERPLEXITY_API_KEY")
        if not self._api_key:
            self._logger.warning("PERPLEXITY_API_KEY not found in environment variables")

        self._base_url = "https://api.perplexity.ai/chat/completions"

    def _run(
        self,
        product_query: str,
        max_retailers: int = 5,
        include_comparison_sites: bool = False
    ) -> str:
        """
        Research UK retailers that sell the specified product.

        Args:
            product_query: Specific product to search for
            max_retailers: Maximum number of retailers to find
            include_comparison_sites: Whether to include price comparison sites

        Returns:
            JSON string with retailer information and product URLs
        """
        try:
            self._logger.info(f"[PERPLEXITY] Researching retailers for: {product_query}")

            if not self._api_key:
                return self._fallback_retailer_result(product_query, max_retailers)

            # Build research prompt
            prompt = self._build_retailer_research_prompt(
                product_query, max_retailers, include_comparison_sites
            )

            # Call Perplexity API
            retailer_data = self._call_perplexity_api(prompt)

            # Parse and structure the response
            structured_result = self._structure_retailer_response(
                retailer_data, product_query, max_retailers
            )

            self._logger.info(f"[PERPLEXITY] Found {len(structured_result.get('retailers', []))} retailers")
            return json.dumps(structured_result, indent=2)

        except Exception as e:
            self._logger.error(f"[PERPLEXITY] Retailer research failed: {e}")
            return self._fallback_retailer_result(product_query, max_retailers)

    def _build_retailer_research_prompt(
        self,
        product_query: str,
        max_retailers: int,
        include_comparison_sites: bool
    ) -> str:
        """Build context-aware prompt for retailer research."""
        
        comparison_instruction = ""
        if not include_comparison_sites:
            comparison_instruction = """
IMPORTANT: EXCLUDE price comparison sites like:
- PriceRunner, Shopping.com, Google Shopping, Kelkoo
- Comparison sites that don't sell products directly
- Affiliate marketing sites that redirect to other retailers
"""

        prompt = f"""Find UK retailers that currently sell "{product_query}" online.

REQUIREMENTS:
1. Focus on legitimate UK retail websites that sell products directly
2. Include major UK retailers like ASDA, Tesco, Waitrose, Amazon UK, eBay UK, Argos, Currys, John Lewis, etc.
3. Provide direct product URLs where possible
4. Include current pricing information if available
5. Limit to {max_retailers} retailers maximum
{comparison_instruction}

RESPONSE FORMAT (JSON):
{{
  "product_query": "{product_query}",
  "retailers": [
    {{
      "name": "Retailer Name",
      "website": "retailer-domain.co.uk",
      "product_url": "direct URL to product page if found",
      "price": "£XX.XX or 'Price not available'",
      "availability": "In stock/Out of stock/Unknown",
      "notes": "Any relevant information about this retailer's offering"
    }}
  ],
  "research_summary": "Brief summary of findings and recommendations"
}}

Focus on finding actual product pages, not just retailer homepages. If you can't find the exact product, suggest similar products or categories where it might be found.

Return ONLY the JSON response, no additional text or formatting."""

        return prompt

    def _call_perplexity_api(self, prompt: str) -> str:
        """Call Perplexity API for retailer research."""
        try:
            headers = {
                "Authorization": f"Bearer {self._api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": "sonar-pro",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are an expert UK retail researcher specializing in finding legitimate online retailers that sell specific products. You have comprehensive knowledge of UK ecommerce sites and can find direct product URLs."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.2,
                "top_p": 0.9
            }
            
            response = requests.post(self._base_url, json=payload, headers=headers, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            retailer_data = result["choices"][0]["message"]["content"].strip()
            
            return retailer_data
            
        except Exception as e:
            self._logger.error(f"Perplexity API call failed: {e}")
            raise

    def _structure_retailer_response(
        self,
        raw_response: str,
        product_query: str,
        max_retailers: int
    ) -> Dict[str, Any]:
        """Structure the Perplexity response into a standardized format."""
        try:
            # Try to parse as JSON first
            if raw_response.strip().startswith('{'):
                parsed_data = json.loads(raw_response)
                
                # Validate and clean the data
                retailers = parsed_data.get('retailers', [])
                
                # Ensure we don't exceed max_retailers
                if len(retailers) > max_retailers:
                    retailers = retailers[:max_retailers]
                
                # Validate each retailer entry
                validated_retailers = []
                for retailer in retailers:
                    if isinstance(retailer, dict) and retailer.get('name') and retailer.get('website'):
                        validated_retailers.append({
                            "name": retailer.get('name', 'Unknown'),
                            "website": retailer.get('website', ''),
                            "product_url": retailer.get('product_url', ''),
                            "price": retailer.get('price', 'Price not available'),
                            "availability": retailer.get('availability', 'Unknown'),
                            "notes": retailer.get('notes', '')
                        })
                
                return {
                    "product_query": product_query,
                    "retailers": validated_retailers,
                    "research_summary": parsed_data.get('research_summary', 'Retailer research completed'),
                    "total_found": len(validated_retailers),
                    "api_available": True
                }
            
            else:
                # Handle non-JSON response by extracting information
                return self._extract_retailers_from_text(raw_response, product_query, max_retailers)
                
        except json.JSONDecodeError:
            # Fallback to text extraction
            return self._extract_retailers_from_text(raw_response, product_query, max_retailers)

    def _extract_retailers_from_text(
        self,
        text_response: str,
        product_query: str,
        max_retailers: int
    ) -> Dict[str, Any]:
        """Extract retailer information from text response."""
        # Simple text parsing fallback
        retailers = []
        
        # Common UK retailers to look for in the response
        uk_retailers = [
            "ASDA", "Tesco", "Waitrose", "Amazon UK", "eBay UK", "Argos",
            "Currys", "John Lewis", "Next", "Marks & Spencer", "Sainsbury's"
        ]
        
        for retailer in uk_retailers:
            if retailer.lower() in text_response.lower() and len(retailers) < max_retailers:
                retailers.append({
                    "name": retailer,
                    "website": f"{retailer.lower().replace(' ', '').replace('uk', '')}.co.uk",
                    "product_url": "",
                    "price": "Price not available",
                    "availability": "Unknown",
                    "notes": f"Found mention of {retailer} in research"
                })
        
        return {
            "product_query": product_query,
            "retailers": retailers,
            "research_summary": "Extracted retailer information from text response",
            "total_found": len(retailers),
            "api_available": True
        }

    def _fallback_retailer_result(self, product_query: str, max_retailers: int) -> str:
        """Provide fallback result when Perplexity is unavailable."""
        # Default UK retailers for common product searches
        default_retailers = [
            {
                "name": "Amazon UK",
                "website": "amazon.co.uk",
                "product_url": "",
                "price": "Price not available",
                "availability": "Unknown",
                "notes": "Major UK online retailer - likely to have most products"
            },
            {
                "name": "eBay UK",
                "website": "ebay.co.uk",
                "product_url": "",
                "price": "Price not available",
                "availability": "Unknown",
                "notes": "UK marketplace - good for electronics and consumer goods"
            },
            {
                "name": "Argos",
                "website": "argos.co.uk",
                "product_url": "",
                "price": "Price not available",
                "availability": "Unknown",
                "notes": "UK general retailer - electronics, home goods, toys"
            },
            {
                "name": "Currys",
                "website": "currys.co.uk",
                "product_url": "",
                "price": "Price not available",
                "availability": "Unknown",
                "notes": "UK electronics specialist"
            },
            {
                "name": "John Lewis",
                "website": "johnlewis.com",
                "product_url": "",
                "price": "Price not available",
                "availability": "Unknown",
                "notes": "UK department store - quality products"
            }
        ]
        
        # Limit to requested number
        retailers = default_retailers[:max_retailers]
        
        result = {
            "product_query": product_query,
            "retailers": retailers,
            "research_summary": "Perplexity API unavailable - using default UK retailers",
            "total_found": len(retailers),
            "api_available": False
        }

        return json.dumps(result, indent=2)
