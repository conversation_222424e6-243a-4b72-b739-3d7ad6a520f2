"""Product Description Enhancement Agent - AI-powered description generation using Perplexity."""

import json
import os
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any
from crewai import Agent, LLM, Task
from pydantic import BaseModel
from ..config.settings import settings
from ..schemas.standardized_product import StandardizedProduct
from ..tools.perplexity_description_tool import PerplexityDescriptionTool


class DescriptionEnhancementResult(BaseModel):
    """Pydantic model for description enhancement task output."""
    enhanced_products: List[Dict[str, Any]]
    enhancement_summary: Dict[str, Any]
    processing_complete: bool = True
    storage_info: Optional[Dict[str, Any]] = None


class ProductDescriptionAgent:
    """AI-powered agent for enhancing product descriptions using Perplexity research."""

    def __init__(self, stagehand_tool=None, verbose: bool = True, tools: List = None, llm: Optional[LLM] = None):
        """Initialize the product description enhancement agent with Perplexity tool."""
        # Create Perplexity description tool
        perplexity_tool = PerplexityDescriptionTool()

        # Handle both old (tools, llm) and new (stagehand_tool, verbose) calling patterns
        if stagehand_tool is not None:
            # New Flow-based calling pattern - add Perplexity tool
            tools = [perplexity_tool]
        elif tools is None:
            tools = [perplexity_tool]
        else:
            # Add Perplexity tool to existing tools
            tools.append(perplexity_tool)

        agent_config = {
            "role": "AI-Powered Product Description Enhancement Specialist",
            "goal": """
            Transform basic product names into comprehensive, engaging descriptions using AI research.
            Enhance product data with detailed descriptions, benefits, usage tips, and key features
            to create marketing-quality content that helps customers make informed decisions.
            """,
            "backstory": """
            You are an expert product content specialist with access to AI-powered research tools.
            Your mission is to transform basic product names into rich, informative descriptions
            that provide real value to customers.

            Your core expertise includes:
            - AI-powered product research using Perplexity
            - Marketing-quality description generation
            - Product benefit and feature identification
            - Category-specific content optimization (food, electronics, etc.)
            - Vendor tone consistency and brand alignment
            - Storage and session management for enhanced product data
            - Quality assurance for generated descriptions
            - Batch processing optimization for multiple products
            - Cross-page duplicate detection and removal
            - UK retail content standards and tone consistency
            - Multi-vendor description alignment across ASDA, Tesco, Waitrose, etc.

            CRITICAL: You focus on description enhancement and storage. You never extract data
            from web pages - that's the ExtractionAgent's job. You enhance the descriptions
            of products they extract using AI-powered research and content generation.
            """,
            "verbose": verbose,
            "allow_delegation": False,
            "tools": tools,
            "max_iter": 3,
            "memory": settings.enable_crew_memory
        }

        if llm:
            agent_config["llm"] = llm

        self.agent = Agent(**agent_config)

    def get_agent(self) -> Agent:
        """Get the CrewAI agent instance."""
        return self.agent

    def create_validation_task(self, vendor: str, category: str, products: List, page_number: int = 1, **kwargs):
        """Alias for create_description_enhancement_task to match Flow calling pattern."""
        return self.create_description_enhancement_task(
            vendor=vendor,
            category=category,
            products=products,
            page_number=page_number,
            session_id=kwargs.get('session_id'),
            max_retries=kwargs.get('max_retries', 3)
        )

    def create_description_enhancement_task(self,
                                         vendor: str,
                                         category: str,
                                         products: List,
                                         page_number: int = 1,
                                         session_id: str = None,
                                         max_retries: int = 3):
        """Create a task for enhancing product descriptions using AI research."""
        from crewai import Task

        task_description = f"""
        Enhance product descriptions using AI-powered research and content generation.

        Vendor: {vendor}
        Category: {category}
        Page Number: {page_number}
        Session ID: {session_id}
        Max Re-extraction Retries: {max_retries}

        DESCRIPTION ENHANCEMENT WORKFLOW:
        1. **Receive extracted product batch from ExtractionAgent**
        2. **Process each product for description enhancement**
        3. **For each product:**
           - Use Perplexity AI to research and generate comprehensive descriptions
           - Transform basic product names into marketing-quality content
           - Include benefits, usage tips, and key features
           - Maintain vendor tone and category consistency
        4. **Quality assurance and validation of enhanced descriptions**
        5. **Manage persistent storage with enhanced product data**
        6. **Handle deduplication and session accumulation across pages**

        DESCRIPTION ENHANCEMENT CRITERIA:
        - Generate rich descriptions for products with basic/missing descriptions
        - Research product benefits and characteristics using AI
        - Include category-specific information (nutritional for food, specs for electronics)
        - Maintain {vendor.upper()} brand tone and style
        - Ensure descriptions are customer-focused and informative
        - Add usage tips, benefits, and key features where relevant

        ENHANCEMENT QUALITY STANDARDS:
        - Descriptions should be 2-3 sentences long
        - Include specific product benefits and characteristics
        - Use engaging, customer-friendly language
        - Maintain consistency across similar products
        - Avoid generic or template-like descriptions

        TOOLS AVAILABLE:
        - perplexity_description_tool: Use this to enhance product descriptions with AI research

        ENHANCEMENT PROCESS:
        1. Access the extracted products from your task context
        2. Call perplexity_description_tool with the following parameters:
           - products_list: The complete list of extracted products from context
           - category: "{category}"
           - vendor: "{vendor}"
           - page_number: {page_number}
        3. The tool will automatically process all products and enhance descriptions where needed
        4. Store the enhanced products with improved descriptions

        IMPORTANT: The extracted products are available in your task context, not in the task description.
        This saves tokens and improves efficiency.

        JSON FILE MANAGEMENT:
        - File Location: ./results/scraping_sessions/
        - File Name: {vendor}_{category}_{session_id}.json
        - Atomic Updates: Create backup before updating
        - Session Accumulation: Merge enhanced products across pages
        - Deduplication: Remove duplicates based on name + price + vendor

        CRITICAL: Focus on description enhancement quality and storage reliability.
        Use the Perplexity tool to generate comprehensive, engaging descriptions.

        ABSOLUTELY FORBIDDEN - JSON COMMENTS:
        - NEVER add // comments in JSON output
        - NEVER add "// Additional products omitted for brevity"
        - NEVER add "// Array of products" or similar comments
        - JSON must be valid and parseable - no comments allowed
        - Comments break JSON parsing and cause system failures
        """

        return Task(
            description=task_description,
            agent=self.agent,
            expected_output=f"""
            Description enhancement and storage result:
            {{
              "validation_complete": true,
              "products_enhanced": <number_of_products_with_enhanced_descriptions>,
              "products_processed": <total_number_of_products_processed>,
              "enhancement_success_rate": <percentage_of_successful_enhancements>,
              "storage_info": {{
                "json_file_path": "./results/scraping_sessions/{vendor}_{category}_{session_id}.json",
                "backup_created": <true/false>,
                "total_products_in_session": <accumulated_products>,
                "duplicates_removed": <number_of_duplicates>
              }},
              "session_stats": {{
                "total_pages_processed": {page_number},
                "total_products_saved": <accumulated_count>,
                "descriptions_enhanced": <number_of_enhanced_descriptions>,
                "ready_for_pagination": <true/false>
              }}
            }}

            Confirm that products with enhanced descriptions are saved to persistent storage.
            Report the success rate of description enhancements using Perplexity AI.
            """
        )

    def create_feedback_generation_task(self, 
                                      validation_issues: List[str],
                                      extraction_metadata: Dict[str, Any],
                                      retry_count: int = 1,
                                      max_retries: int = 3):
        """Create a task for generating specific feedback for re-extraction."""
        from crewai import Task

        task_description = f"""
        Generate specific, actionable feedback for ExtractionAgent re-extraction.
        
        Validation Issues: {validation_issues}
        Extraction Metadata: {extraction_metadata}
        Retry Count: {retry_count} of {max_retries}

        FEEDBACK GENERATION REQUIREMENTS:
        1. **Analyze specific validation failures**
        2. **Identify root causes of extraction issues**
        3. **Generate actionable improvement suggestions**
        4. **Provide alternative extraction strategies**
        5. **Specify quality requirements for re-extraction**

        ISSUE ANALYSIS:
        - Categorize validation failures by type
        - Identify patterns in missing or invalid data
        - Determine if issues are systematic or isolated
        - Assess extraction method effectiveness

        SUGGESTION GENERATION:
        - Provide specific selector alternatives
        - Suggest different extraction approaches
        - Recommend data cleaning improvements
        - Offer quality verification steps

        FEEDBACK QUALITY:
        - Be specific and actionable
        - Focus on fixable issues
        - Provide clear improvement guidance
        - Include success criteria for re-extraction

        CRITICAL: Generate feedback that leads to measurable improvement.
        Focus on the most impactful changes for data quality enhancement.
        """

        return Task(
            description=task_description,
            agent=self.agent,
            expected_output=f"""
            Detailed feedback for re-extraction:
            {{
              "validation_result": "failed",
              "feedback": {{
                "issues": [

                ],
                "suggestions": [

                ],
                "alternative_strategies": [

                ],
                "quality_requirements": {{

                }}
              }},
              "retry_count": {retry_count},
              "max_retries": {max_retries},
              "improvement_focus": "Primary areas for extraction improvement"
            }}
            
            Provide specific, actionable feedback that addresses root causes.
            """
        )

    def create_json_storage_task(self,
                               vendor: str,
                               category: str,
                               session_id: str,
                               products_to_save: List[Dict[str, Any]],
                               page_number: int = 1):
        """Create a task for managing JSON file storage and session accumulation."""
        from crewai import Task

        task_description = f"""
        Manage persistent JSON storage with session accumulation and backup creation.

        Vendor: {vendor}
        Category: {category}
        Session ID: {session_id}
        Products to Save: {len(products_to_save)} products
        Page Number: {page_number}

        JSON STORAGE REQUIREMENTS:
        1. **Create or update session JSON file**
        2. **Implement atomic file operations with backup**
        3. **Accumulate products across multiple pages**
        4. **Handle deduplication within session**
        5. **Update session metadata and statistics**

        FILE STRUCTURE:
        {{
          "scraping_session": {{
            "session_id": "{session_id}",
            "vendor": "{vendor}",
            "category": "{category}",
            "started_at": "ISO timestamp",
            "last_updated": "ISO timestamp",
            "status": "in_progress",
            "pagination_info": {{
              "total_pages_processed": {page_number},
              "current_page": {page_number}
            }}
          }},
          "products": [

          ],
          "session_statistics": {{
            "total_products_found": <count>,
            "total_products_validated": <count>,
            "validation_success_rate": <percentage>
          }}
        }}

        ATOMIC OPERATIONS:
        1. Create backup of existing file (if exists)
        2. Read current session data
        3. Merge new products with existing
        4. Remove duplicates based on name + price + vendor
        5. Update session metadata
        6. Write to temporary file
        7. Rename temporary file to final name
        8. Verify write success

        DEDUPLICATION STRATEGY:
        - Compare products by: name + price.amount + vendor
        - Keep most recent version of duplicates
        - Log duplicate removal for tracking

        CRITICAL: Ensure data integrity and atomic file operations.
        Never lose existing data during updates.
        """

        return Task(
            description=task_description,
            agent=self.agent,
            expected_output=f"""
            JSON storage completion report:
            {{
              "storage_complete": true,
              "file_info": {{
                "file_path": "./results/scraping_sessions/{vendor}_{category}_{session_id}.json",
                "backup_created": <true/false>,
                "file_size_bytes": <file_size>,
                "write_successful": true
              }},
              "data_info": {{
                "products_added": <new_products_count>,
                "products_updated": <updated_products_count>,
                "duplicates_removed": <duplicate_count>,
                "total_products_in_session": <total_count>
              }},
              "session_updated": {{
                "pages_processed": {page_number},
                "last_updated": "ISO timestamp",
                "status": "in_progress"
              }}
            }}

            Confirm successful atomic storage operation with data integrity.
            """
        )

    def create_deduplication_task(self,
                                products: List[Dict[str, Any]],
                                existing_products: List[Dict[str, Any]] = None):
        """Create a task for comprehensive product deduplication."""
        from crewai import Task

        existing_count = len(existing_products) if existing_products else 0

        task_description = f"""
        Perform comprehensive deduplication across product datasets.

        New Products: {len(products)} products
        Existing Products: {existing_count} products
        Total to Process: {len(products) + existing_count} products

        DEDUPLICATION STRATEGY:
        1. **Primary matching**: name + price.amount + vendor
        2. **Secondary matching**: Similar names (fuzzy matching) + vendor
        3. **Tertiary matching**: Same image_url + vendor
        4. **Keep most recent**: Prefer products with later scraped_at timestamps

        MATCHING CRITERIA:
        - Exact match: Identical name, price, and vendor
        - Fuzzy match: >90% name similarity + same vendor + similar price (±5%)
        - Image match: Same image_url + same vendor
        - Category match: Same category + vendor + similar name

        DEDUPLICATION PROCESS:
        1. Group products by potential duplicates
        2. Apply matching criteria in priority order
        3. Select best representative from each group
        4. Preserve most complete product data
        5. Log all deduplication decisions

        QUALITY PRESERVATION:
        - Keep products with more complete data
        - Prefer products with descriptions over those without
        - Maintain products with valid image URLs
        - Preserve weight information when available

        CRITICAL: Maintain data quality while removing true duplicates.
        Err on the side of keeping products rather than over-deduplicating.
        """

        return Task(
            description=task_description,
            agent=self.agent,
            expected_output=f"""
            Deduplication completion report:
            {{
              "deduplication_complete": true,
              "deduplication_stats": {{
                "input_products": {len(products) + existing_count},
                "output_products": <final_unique_count>,
                "duplicates_removed": <removed_count>,
                "deduplication_rate": <percentage>
              }},
              "matching_breakdown": {{
                "exact_matches": <count>,
                "fuzzy_matches": <count>,
                "image_matches": <count>,
                "no_matches": <count>
              }},
              "deduplicated_products": [

              ]
            }}

            Return only unique products with highest quality data preserved.
            """
        )
