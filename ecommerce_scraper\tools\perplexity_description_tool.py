"""Perplexity-powered Product Description Enhancement Tool."""

import json
import logging
from typing import Dict, Any, List, Optional
from crewai.tools import BaseTool
from pydantic import BaseModel, Field
import requests
import os

logger = logging.getLogger(__name__)


class ProductDescriptionInput(BaseModel):
    """Input schema for product description enhancement."""
    products_list: List[Dict[str, Any]] = Field(..., description="List of extracted products to enhance descriptions for")
    category: str = Field(..., description="Product category (e.g., 'fruit', 'electronics')")
    vendor: str = Field(..., description="Vendor name (e.g., 'asda', 'tesco')")
    page_number: int = Field(1, description="Current page number for context")


class PerplexityDescriptionTool(BaseTool):
    """Perplexity-powered tool for generating comprehensive product descriptions."""

    name: str = "perplexity_description_tool"
    description: str = """
    AI-powered product description enhancement tool using Perplexity AI.
    
    Features:
    - Generate comprehensive, marketing-quality product descriptions
    - Research product benefits, uses, and characteristics
    - Create engaging, informative descriptions from basic product names
    - Maintain consistency with vendor tone and category standards
    - Include nutritional info, usage tips, and key features where relevant
    
    Use this tool to transform basic product names into rich, detailed descriptions
    that provide value to customers and improve product presentation.
    """
    args_schema: type[BaseModel] = ProductDescriptionInput

    def __init__(self):
        """Initialize the Perplexity description tool."""
        super().__init__()
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

        # Get API key from environment or config
        self._api_key = os.getenv("PERPLEXITY_API_KEY")
        if not self._api_key:
            self._logger.warning("PERPLEXITY_API_KEY not found in environment variables")

        self._base_url = "https://api.perplexity.ai/chat/completions"

    def _run(
        self,
        products_list: List[Dict[str, Any]],
        category: str,
        vendor: str,
        page_number: int = 1
    ) -> str:
        """
        Generate enhanced descriptions for a batch of products using Perplexity AI.

        Args:
            products_list: List of extracted products to enhance
            category: Product category
            vendor: Vendor name
            page_number: Current page number for context

        Returns:
            Enhanced products with improved descriptions as JSON string
        """
        try:
            self._logger.info(f"[PERPLEXITY] Enhancing descriptions for {len(products_list)} products")

            enhanced_products = []
            enhancement_stats = {
                "total_products": len(products_list),
                "enhanced_count": 0,
                "failed_count": 0,
                "api_available": bool(self._api_key)
            }

            for product in products_list:
                try:
                    # Extract product details
                    product_name = product.get("name", "")
                    current_desc = product.get("description", "")
                    price = product.get("price", {})
                    weight = product.get("weight", "")

                    # Skip if no product name
                    if not product_name:
                        enhanced_products.append(product)
                        continue

                    # Generate enhanced description
                    if self._api_key and self._should_enhance_description(current_desc, product_name):
                        enhanced_desc = self._enhance_single_product(
                            product_name, category, vendor, current_desc, price, weight
                        )

                        # Update product with enhanced description
                        enhanced_product = product.copy()
                        enhanced_product["description"] = enhanced_desc
                        enhanced_product["description_enhanced"] = True
                        enhanced_products.append(enhanced_product)
                        enhancement_stats["enhanced_count"] += 1

                    else:
                        # Keep original product
                        enhanced_products.append(product)

                except Exception as e:
                    self._logger.error(f"[PERPLEXITY] Failed to enhance {product_name}: {e}")
                    enhanced_products.append(product)
                    enhancement_stats["failed_count"] += 1

            # Format result
            result = {
                "enhanced_products": enhanced_products,
                "enhancement_stats": enhancement_stats,
                "category": category,
                "vendor": vendor,
                "page_number": page_number,
                "processing_complete": True
            }

            self._logger.info(f"[PERPLEXITY] Batch enhancement completed: {enhancement_stats['enhanced_count']}/{enhancement_stats['total_products']} enhanced")
            return json.dumps(result, indent=2)

        except Exception as e:
            self._logger.error(f"[PERPLEXITY] Batch enhancement failed: {e}")
            return self._fallback_batch_result(products_list, category, vendor, page_number)

    def _build_enhancement_prompt(
        self,
        product_name: str,
        category: str,
        vendor: str,
        current_description: Optional[str],
        price: Dict[str, Any],
        weight: str
    ) -> str:
        """Build context-aware prompt for description enhancement."""
        
        # Base prompt structure
        prompt = f"""Generate a comprehensive, engaging product description for "{product_name}" from {vendor.upper()}.

Product Context:
- Category: {category}
- Vendor: {vendor.upper()}"""
        
        if price and isinstance(price, dict):
            amount = price.get("amount", "")
            currency = price.get("currency", "")
            if amount and currency:
                prompt += f"\n- Price: {currency}{amount}"
        elif price:
            prompt += f"\n- Price: {price}"
        if weight:
            prompt += f"\n- Size/Weight: {weight}"
        if current_description:
            prompt += f"\n- Current Description: {current_description}"

        prompt += f"""

Requirements:
1. Create a detailed, marketing-quality description (2-3 sentences)
2. Include key benefits, uses, or characteristics relevant to {category}
3. Maintain {vendor.upper()}'s tone (friendly, informative, value-focused)
4. Add relevant details like nutritional benefits, usage tips, or key features
5. Make it engaging and informative for online shoppers
6. Keep it concise but comprehensive

Focus on what makes this product appealing and useful to customers.
Return ONLY the enhanced description text, no additional formatting or explanations."""

        return prompt

    def _call_perplexity_api(self, prompt: str) -> str:
        """Call Perplexity API for description enhancement."""
        try:
            headers = {
                "Authorization": f"Bearer {self._api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": "sonar-pro",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are an expert product description writer specializing in ecommerce content. Create engaging, informative descriptions that help customers understand product value."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": 300,
                "temperature": 0.3,
                "top_p": 0.9
            }
            
            response = requests.post(self._base_url, json=payload, headers=headers, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            enhanced_description = result["choices"][0]["message"]["content"].strip()
            
            return enhanced_description
            
        except Exception as e:
            self._logger.error(f"Perplexity API call failed: {e}")
            raise

    def _should_enhance_description(self, current_desc: str, product_name: str) -> bool:
        """Determine if a product description should be enhanced."""
        if not current_desc or current_desc.strip() == "":
            return True

        # Enhance if description is just the product name
        if current_desc.strip().lower() == product_name.strip().lower():
            return True

        # Enhance if description is very short (less than 20 characters)
        if len(current_desc.strip()) < 20:
            return True

        return False

    def _enhance_single_product(
        self,
        product_name: str,
        category: str,
        vendor: str,
        current_desc: str,
        price: Dict[str, Any],
        weight: str
    ) -> str:
        """Enhance description for a single product."""
        try:
            # Build context-aware prompt
            prompt = self._build_enhancement_prompt(
                product_name, category, vendor, current_desc, price, weight
            )

            # Call Perplexity API
            enhanced_description = self._call_perplexity_api(prompt)
            return enhanced_description

        except Exception as e:
            self._logger.error(f"Single product enhancement failed: {e}")
            return current_desc or f"High-quality {product_name.lower()} available for purchase."

    def _fallback_batch_result(self, products_list: List[Dict[str, Any]], category: str, vendor: str, page_number: int) -> str:
        """Provide fallback result when Perplexity is unavailable."""
        result = {
            "enhanced_products": products_list,  # Return original products
            "enhancement_stats": {
                "total_products": len(products_list),
                "enhanced_count": 0,
                "failed_count": len(products_list),
                "api_available": False
            },
            "category": category,
            "vendor": vendor,
            "page_number": page_number,
            "processing_complete": True,
            "note": "Perplexity API unavailable - using original descriptions"
        }

        return json.dumps(result, indent=2)

    def _sanitize_product_name(self, product_name: str) -> str:
        """Clean and sanitize product name for better API results."""
        # Remove common prefixes/suffixes that don't add value
        prefixes_to_remove = ["ASDA", "TESCO", "JUST ESSENTIALS by"]
        
        cleaned_name = product_name
        for prefix in prefixes_to_remove:
            if cleaned_name.startswith(prefix):
                cleaned_name = cleaned_name[len(prefix):].strip()
        
        return cleaned_name
