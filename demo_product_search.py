"""Demonstration script for the product-specific search system."""

import os
import sys
import json
from pathlib import Path
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()


def demo_schema_creation():
    """Demonstrate schema creation and validation."""
    console.print(Panel.fit(
        "[bold blue]📋 Schema Creation Demo[/bold blue]\n\n"
        "Creating product search result schemas with validation...",
        title="Demo 1",
        border_style="blue"
    ))
    
    try:
        from ecommerce_scraper.schemas.product_search_result import ProductSearchResult, ProductSearchItem
        
        # Create sample product items
        products = [
            ProductSearchItem(
                product_name="Apple iPhone 15 Pro 128GB",
                price="£999.99",
                url="https://amazon.co.uk/apple-iphone-15-pro",
                retailer="Amazon UK"
            ),
            ProductSearchItem(
                product_name="iPhone 15 Pro 256GB Space Black",
                price="£1149.99",
                url="https://argos.co.uk/product/iphone-15-pro",
                retailer="Argos"
            ),
            ProductSearchItem(
                product_name="Apple iPhone 15 Pro Max",
                price="£1199.99",
                url="https://currys.co.uk/products/apple-iphone-15-pro-max",
                retailer="Currys"
            )
        ]
        
        # Create search result
        result = ProductSearchResult(
            search_query="iPhone 15 Pro",
            results=products,
            metadata={
                "demo": True,
                "retailers_searched": 3,
                "success_rate": 1.0
            }
        )
        
        # Display results table
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Retailer", style="cyan", no_wrap=True)
        table.add_column("Product Name", style="green")
        table.add_column("Price", style="yellow", no_wrap=True)
        table.add_column("URL", style="blue")
        
        for product in result.results:
            table.add_row(
                product.retailer,
                product.product_name,
                product.price,
                product.url[:40] + "..." if len(product.url) > 40 else product.url
            )
        
        console.print(table)
        
        # Show summary
        summary = result.get_summary()
        console.print(f"\n[green]✅ Created search result with {summary['total_products_found']} products[/green]")
        console.print(f"[cyan]Retailers: {', '.join(summary['retailers'])}[/cyan]")
        
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Schema demo failed: {e}[/red]")
        return False


def demo_retailer_research():
    """Demonstrate retailer research tool."""
    console.print(Panel.fit(
        "[bold blue]🔬 Retailer Research Demo[/bold blue]\n\n"
        "Using AI-powered retailer research to find UK retailers...",
        title="Demo 2",
        border_style="blue"
    ))
    
    try:
        from ecommerce_scraper.tools.perplexity_retailer_research_tool import PerplexityRetailerResearchTool
        
        # Create research tool
        tool = PerplexityRetailerResearchTool()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Researching retailers for 'Samsung Galaxy S24'...", total=None)
            
            # Research retailers (will use fallback if no API key)
            result = tool._run(
                product_query="Samsung Galaxy S24",
                max_retailers=5,
                include_comparison_sites=False
            )
            
            progress.update(task, completed=1, total=1)
        
        # Parse and display results
        research_data = json.loads(result)
        
        console.print(f"\n[green]✅ Research completed for: {research_data['product_query']}[/green]")
        console.print(f"[cyan]Found {research_data['total_found']} retailers[/cyan]")
        console.print(f"[yellow]API Available: {research_data['api_available']}[/yellow]")
        
        # Display retailers table
        if research_data['retailers']:
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("Retailer", style="cyan")
            table.add_column("Website", style="green")
            table.add_column("Price", style="yellow")
            table.add_column("Notes", style="white")
            
            for retailer in research_data['retailers']:
                table.add_row(
                    retailer.get('name', 'Unknown'),
                    retailer.get('website', 'N/A'),
                    retailer.get('price', 'N/A'),
                    retailer.get('notes', 'N/A')[:50] + "..." if len(retailer.get('notes', '')) > 50 else retailer.get('notes', 'N/A')
                )
            
            console.print(table)
        
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Retailer research demo failed: {e}[/red]")
        return False


def demo_validation_system():
    """Demonstrate the validation system."""
    console.print(Panel.fit(
        "[bold blue]✅ Validation System Demo[/bold blue]\n\n"
        "Testing product validation with semantic matching...",
        title="Demo 3",
        border_style="blue"
    ))
    
    try:
        from ecommerce_scraper.agents.product_search_validation_agent import ProductSearchValidationAgent
        
        # Create validation agent
        agent = ProductSearchValidationAgent(verbose=False)
        
        # Test cases for product matching
        test_cases = [
            ("iPhone 15 Pro", "Apple iPhone 15 Pro 128GB", "Exact match with brand"),
            ("Samsung Galaxy S24", "Galaxy S24 Ultra 256GB", "Partial match - similar model"),
            ("Nike Air Max 90", "Nike Air Max 90 White", "Exact match with color"),
            ("MacBook Pro", "Dell Laptop", "No match - different product"),
            ("PlayStation 5", "PS5 Console", "Match with abbreviation")
        ]
        
        # Create validation results table
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Search Query", style="cyan")
        table.add_column("Product Name", style="green")
        table.add_column("Match Score", style="yellow")
        table.add_column("Result", style="white")
        
        for search_query, product_name, description in test_cases:
            score = agent.validate_product_match(product_name, search_query)
            result = "✅ Match" if score >= 0.7 else "❌ No Match"
            
            table.add_row(
                search_query,
                product_name,
                f"{score:.2f}",
                result
            )
        
        console.print(table)
        
        # Test UK retailer validation
        console.print(f"\n[bold]UK Retailer Validation Tests:[/bold]")
        retailer_tests = [
            ("https://amazon.co.uk/product", "Amazon UK"),
            ("https://tesco.com/groceries", "Tesco"),
            ("https://pricerunner.com/product", "Price Comparison Site"),
            ("https://unknown-site.com/product", "Unknown Site")
        ]
        
        for url, description in retailer_tests:
            is_valid = agent.is_legitimate_uk_retailer(url)
            status = "✅ Valid" if is_valid else "❌ Invalid"
            console.print(f"  {status} {description}: {url}")
        
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Validation demo failed: {e}[/red]")
        return False


def demo_result_storage():
    """Demonstrate result storage and retrieval."""
    console.print(Panel.fit(
        "[bold blue]💾 Result Storage Demo[/bold blue]\n\n"
        "Saving and loading product search results...",
        title="Demo 4",
        border_style="blue"
    ))
    
    try:
        from ecommerce_scraper.workflows.flow_utils import FlowResultProcessor
        
        # Create processor
        session_id = f"demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        processor = FlowResultProcessor(session_id=session_id, verbose=False)
        
        # Create demo result data
        demo_result = {
            "search_query": "Nintendo Switch OLED",
            "results": [
                {
                    "product_name": "Nintendo Switch OLED Console",
                    "price": "£309.99",
                    "url": "https://argos.co.uk/product/nintendo-switch-oled",
                    "retailer": "Argos",
                    "timestamp": datetime.now().isoformat()
                },
                {
                    "product_name": "Nintendo Switch OLED White",
                    "price": "£319.99",
                    "url": "https://currys.co.uk/products/nintendo-switch-oled",
                    "retailer": "Currys",
                    "timestamp": datetime.now().isoformat()
                }
            ],
            "metadata": {
                "session_id": session_id,
                "retailers_searched": 2,
                "total_attempts": 2,
                "success_rate": 1.0,
                "demo": True
            }
        }
        
        # Save results
        file_path = processor.save_product_search_results(demo_result)
        
        if file_path and file_path.exists():
            console.print(f"[green]✅ Results saved to: {file_path}[/green]")
            
            # Load and verify results
            loaded_result = processor.load_product_search_results(file_path)
            
            if loaded_result:
                console.print(f"[green]✅ Results loaded successfully[/green]")
                console.print(f"[cyan]Search Query: {loaded_result['search_query']}[/cyan]")
                console.print(f"[cyan]Products Found: {len(loaded_result['results'])}[/cyan]")
                console.print(f"[cyan]Success Rate: {loaded_result['metadata']['success_rate']:.1%}[/cyan]")
                
                # Display products
                for i, product in enumerate(loaded_result['results'], 1):
                    console.print(f"  {i}. {product['product_name']} - {product['price']} ({product['retailer']})")
            
            # Cleanup demo file
            file_path.unlink()
            console.print(f"[yellow]🧹 Demo file cleaned up[/yellow]")
            
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Result storage demo failed: {e}[/red]")
        return False


def main():
    """Run all demonstration scripts."""
    console.print(Panel.fit(
        "[bold green]🚀 Product-Specific Search System Demo[/bold green]\n\n"
        "[cyan]This demo showcases the key components of the product search system:[/cyan]\n"
        "• Schema creation and validation\n"
        "• AI-powered retailer research\n"
        "• Intelligent product validation\n"
        "• Result storage and retrieval\n\n"
        "[yellow]Note: This demo uses test data and fallback mechanisms.[/yellow]",
        title="Product Search Demo",
        border_style="green"
    ))
    
    demos = [
        ("Schema Creation", demo_schema_creation),
        ("Retailer Research", demo_retailer_research),
        ("Validation System", demo_validation_system),
        ("Result Storage", demo_result_storage)
    ]
    
    results = []
    
    for demo_name, demo_func in demos:
        console.print(f"\n{'='*60}")
        success = demo_func()
        results.append((demo_name, success))
        
        if success:
            console.print(f"[green]✅ {demo_name} demo completed successfully[/green]")
        else:
            console.print(f"[red]❌ {demo_name} demo failed[/red]")
    
    # Final summary
    console.print(f"\n{'='*60}")
    console.print(Panel.fit(
        "[bold blue]📊 Demo Summary[/bold blue]\n\n" +
        "\n".join([
            f"{'✅' if success else '❌'} {name}"
            for name, success in results
        ]) + f"\n\n[cyan]Completed: {sum(1 for _, s in results if s)}/{len(results)} demos[/cyan]",
        title="Results",
        border_style="blue"
    ))
    
    if all(success for _, success in results):
        console.print("\n[bold green]🎉 All demos completed successfully![/bold green]")
        console.print("[cyan]The product search system is ready for use.[/cyan]")
        console.print("\n[yellow]To run the actual scraper:[/yellow]")
        console.print("[white]python product_search_scraper.py[/white]")
    else:
        console.print("\n[bold yellow]⚠️ Some demos had issues.[/bold yellow]")
        console.print("[cyan]Check the implementation for any problems.[/cyan]")


if __name__ == "__main__":
    main()
